"""
Telegram inline keyboards for the bot
"""

from __future__ import annotations
from typing import Dict, List, Optional

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton


def main_menu_keyboard() -> InlineKeyboardMarkup:
    """Create main menu keyboard with essential features only"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="💼 Wallet", callback_data="menu:wallet"),
                InlineKeyboardButton(text="🔎 Browse", callback_data="menu:browse"),
            ],
            [
                InlineKeyboardButton(text="🛒 Cart", callback_data="local:cart:view"),
                InlineKeyboardButton(text="📦 Orders", callback_data="menu:orders"),
            ],
            [
                InlineKeyboardButton(text="📜 History", callback_data="menu:history"),
                InlineKeyboardButton(text="⚙️ Settings", callback_data="menu:settings"),
                InlineKeyboardButton(text="❓ Help", callback_data="menu:help"),
            ],
        ]
    )


def wallet_menu_keyboard() -> InlineKeyboardMarkup:
    """Create wallet menu keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="➕ Add $10", callback_data="wallet:add10"),
                InlineKeyboardButton(text="➕ Add $25", callback_data="wallet:add25"),
            ],
            [
                InlineKeyboardButton(text="➕ Add $50", callback_data="wallet:add50"),
                InlineKeyboardButton(
                    text="✏️ Add Custom", callback_data="wallet:add_custom"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📊 Statistics", callback_data="wallet:stats"
                ),
                InlineKeyboardButton(
                    text="📄 Export", callback_data="wallet:export_csv"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")],
        ]
    )


def browse_menu_keyboard() -> InlineKeyboardMarkup:
    """Create browse/catalog menu keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="🔎 Search", callback_data="catalog:search"),
                InlineKeyboardButton(
                    text="🧰 Filters", callback_data="catalog:filters"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")],
        ]
    )


def filter_menu_keyboard(
    category_status: Optional[Dict[str, bool]] = None,
) -> InlineKeyboardMarkup:
    """Create filter menu keyboard with hierarchical categories"""

    category_status = category_status or {}

    def _label(key: str, base: str) -> str:
        return f"✅ {base}" if category_status.get(key) else base

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=_label("location", "📍 Location"),
                    callback_data="filter:category:location",
                ),
                InlineKeyboardButton(
                    text=_label("card", "💳 Card"),
                    callback_data="filter:category:card",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=_label("pricing", "💲 Pricing"),
                    callback_data="filter:category:pricing",
                ),
                InlineKeyboardButton(
                    text=_label("contact", "🧾 Contact"),
                    callback_data="filter:category:contact",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=_label("identity", "🛡 Identity"),
                    callback_data="filter:category:identity",
                ),
                InlineKeyboardButton(
                    text=_label("extras", "✨ Extras"),
                    callback_data="filter:category:extras",
                ),
            ],
            [
                InlineKeyboardButton(text="🧹 Clear All", callback_data="filter:clear"),
                InlineKeyboardButton(text="✅ Apply", callback_data="filter:apply"),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:browse")],
        ]
    )


def purchase_confirmation_keyboard(item_id: str) -> InlineKeyboardMarkup:
    """Create purchase confirmation keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Purchase",
                    callback_data=f"purchase:confirm:{item_id}",
                ),
                InlineKeyboardButton(text="❌ Cancel", callback_data="purchase:cancel"),
            ]
        ]
    )


def admin_menu_keyboard() -> InlineKeyboardMarkup:
    """Create enhanced admin menu keyboard with better organization"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            # Quick Actions Row
            [
                InlineKeyboardButton(
                    text="📊 Dashboard", callback_data="admin:dashboard"
                ),
                InlineKeyboardButton(
                    text="🚨 System Health", callback_data="admin:health"
                ),
            ],
            # User Management Row
            [
                InlineKeyboardButton(text="👥 Users", callback_data="admin:users"),
                InlineKeyboardButton(
                    text="💳 Transactions", callback_data="admin:transactions"
                ),
            ],
            # System Management Row
            [
                InlineKeyboardButton(text="🔧 APIs", callback_data="api_config_main"),
                InlineKeyboardButton(text="🗂️ Database", callback_data="admin:database"),
            ],
            # Configuration Row
            [
                InlineKeyboardButton(text="⚙️ Settings", callback_data="admin:settings"),
                InlineKeyboardButton(text="📋 Audit Logs", callback_data="admin:audit"),
            ],
            # Emergency Controls Row
            [
                InlineKeyboardButton(
                    text="🚨 Emergency", callback_data="admin:emergency"
                ),
                InlineKeyboardButton(text="❓ Help", callback_data="admin:help"),
            ],
            # Navigation
            [InlineKeyboardButton(text="🔙 Back to Main", callback_data="menu:main")],
        ]
    )


def pagination_keyboard(
    current_page: int,
    total_pages: int,
    callback_prefix: str,
    back_callback: str = "menu:main",
) -> InlineKeyboardMarkup:
    """Create pagination keyboard"""
    buttons = []

    # Navigation buttons
    nav_buttons = []
    if current_page > 1:
        nav_buttons.append(
            InlineKeyboardButton(
                text="⬅️ Prev", callback_data=f"{callback_prefix}:{current_page-1}"
            )
        )

    nav_buttons.append(
        InlineKeyboardButton(text=f"{current_page}/{total_pages}", callback_data="noop")
    )

    if current_page < total_pages:
        nav_buttons.append(
            InlineKeyboardButton(
                text="Next ➡️", callback_data=f"{callback_prefix}:{current_page+1}"
            )
        )

    if nav_buttons:
        buttons.append(nav_buttons)

    # Back button
    buttons.append([InlineKeyboardButton(text="⬅️ Back", callback_data=back_callback)])

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def create_pagination_keyboard(
    current_page: int,
    total_pages: int,
    callback_prefix: str,
    back_callback: str = "menu:main",
) -> InlineKeyboardMarkup:
    """Backward-compatible wrapper for pagination keyboard.

    Some handlers import create_pagination_keyboard; delegate to pagination_keyboard.
    """
    return pagination_keyboard(
        current_page, total_pages, callback_prefix, back_callback
    )


def yes_no_keyboard(yes_callback: str, no_callback: str) -> InlineKeyboardMarkup:
    """Create yes/no confirmation keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="✅ Yes", callback_data=yes_callback),
                InlineKeyboardButton(text="❌ No", callback_data=no_callback),
            ]
        ]
    )


def back_keyboard(callback_data: str = "menu:main") -> InlineKeyboardMarkup:
    """Create simple back keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text="⬅️ Back", callback_data=callback_data)]
        ]
    )


def admin_dashboard_keyboard() -> InlineKeyboardMarkup:
    """Create admin dashboard keyboard with quick actions"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="👥 Recent Users", callback_data="admin:users:recent"
                ),
                InlineKeyboardButton(
                    text="💳 Recent Transactions",
                    callback_data="admin:transactions:recent",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📊 Daily Report", callback_data="admin:reports:daily"
                ),
                InlineKeyboardButton(
                    text="📈 Analytics", callback_data="admin:analytics"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Refresh", callback_data="admin:dashboard"
                ),
                InlineKeyboardButton(
                    text="📤 Export Report", callback_data="admin:export:report"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def admin_system_health_keyboard() -> InlineKeyboardMarkup:
    """Create system health monitoring keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔍 Check All", callback_data="admin:health:check"
                ),
                InlineKeyboardButton(
                    text="📊 Performance", callback_data="admin:health:performance"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="💾 Database", callback_data="admin:health:database"
                ),
                InlineKeyboardButton(text="🔧 APIs", callback_data="admin:health:apis"),
            ],
            [
                InlineKeyboardButton(
                    text="📈 Metrics", callback_data="admin:health:metrics"
                ),
                InlineKeyboardButton(
                    text="⚠️ Alerts", callback_data="admin:health:alerts"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def admin_database_keyboard() -> InlineKeyboardMarkup:
    """Create database management keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📊 Statistics", callback_data="admin:db:stats"
                ),
                InlineKeyboardButton(
                    text="🧹 Cleanup", callback_data="admin:db:cleanup"
                ),
            ],
            [
                InlineKeyboardButton(text="💾 Backup", callback_data="admin:db:backup"),
                InlineKeyboardButton(
                    text="🔄 Maintenance", callback_data="admin:db:maintenance"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔍 Query Tool", callback_data="admin:db:query"
                ),
                InlineKeyboardButton(
                    text="📈 Performance", callback_data="admin:db:performance"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def admin_emergency_keyboard() -> InlineKeyboardMarkup:
    """Create emergency controls keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🚨 Disable Bot", callback_data="admin:emergency:disable"
                ),
                InlineKeyboardButton(
                    text="📢 Broadcast", callback_data="admin:emergency:broadcast"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔒 Lock Accounts", callback_data="admin:emergency:lock"
                ),
                InlineKeyboardButton(
                    text="🛑 Stop Services", callback_data="admin:emergency:stop"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Restart Bot", callback_data="admin:emergency:restart"
                ),
                InlineKeyboardButton(
                    text="🧹 Clear Cache", callback_data="admin:emergency:cache"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def admin_transactions_keyboard() -> InlineKeyboardMarkup:
    """Create transaction monitoring keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📊 Overview", callback_data="admin:tx:overview"
                ),
                InlineKeyboardButton(text="🔍 Search", callback_data="admin:tx:search"),
            ],
            [
                InlineKeyboardButton(
                    text="⚠️ Suspicious", callback_data="admin:tx:suspicious"
                ),
                InlineKeyboardButton(
                    text="🚫 Flagged", callback_data="admin:tx:flagged"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📈 Analytics", callback_data="admin:tx:analytics"
                ),
                InlineKeyboardButton(text="📤 Export", callback_data="admin:tx:export"),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def admin_audit_keyboard() -> InlineKeyboardMarkup:
    """Create audit logs keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📋 Recent", callback_data="admin:audit:recent"
                ),
                InlineKeyboardButton(
                    text="🔍 Search", callback_data="admin:audit:search"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="👤 User Actions", callback_data="admin:audit:users"
                ),
                InlineKeyboardButton(
                    text="🛠️ Admin Actions", callback_data="admin:audit:admin"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="⚠️ Security", callback_data="admin:audit:security"
                ),
                InlineKeyboardButton(
                    text="📤 Export", callback_data="admin:audit:export"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def admin_help_keyboard() -> InlineKeyboardMarkup:
    """Create admin help keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📖 User Guide", callback_data="admin:help:guide"
                ),
                InlineKeyboardButton(
                    text="🔧 Commands", callback_data="admin:help:commands"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🚨 Emergency", callback_data="admin:help:emergency"
                ),
                InlineKeyboardButton(
                    text="🔐 Security", callback_data="admin:help:security"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📊 Reports", callback_data="admin:help:reports"
                ),
                InlineKeyboardButton(
                    text="🛠️ Troubleshooting", callback_data="admin:help:troubleshoot"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")],
        ]
    )


def confirmation_keyboard(action: str, item_id: str = "") -> InlineKeyboardMarkup:
    """Create confirmation keyboard for destructive actions"""
    confirm_data = (
        f"admin:confirm:{action}:{item_id}" if item_id else f"admin:confirm:{action}"
    )
    cancel_data = (
        f"admin:cancel:{action}:{item_id}" if item_id else f"admin:cancel:{action}"
    )

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="✅ Confirm", callback_data=confirm_data),
                InlineKeyboardButton(text="❌ Cancel", callback_data=cancel_data),
            ],
        ]
    )


def help_menu_keyboard() -> InlineKeyboardMarkup:
    """Create help menu keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🚀 Getting Started", callback_data="help:getting_started"
                ),
                InlineKeyboardButton(text="❓ FAQ", callback_data="help:faq"),
            ],
            [
                InlineKeyboardButton(text="🤖 Commands", callback_data="help:commands"),
                InlineKeyboardButton(text="📞 Support", callback_data="help:support"),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main")],
        ]
    )


def api_management_keyboard() -> InlineKeyboardMarkup:
    """Create API management menu keyboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📋 List APIs", callback_data="admin:apis:list"
                ),
                InlineKeyboardButton(text="➕ Add API", callback_data="admin:apis:add"),
            ],
            [
                InlineKeyboardButton(
                    text="🔍 Search APIs", callback_data="admin:apis:search"
                ),
                InlineKeyboardButton(
                    text="📊 Analytics", callback_data="admin:apis:analytics"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🏥 Health Status", callback_data="admin:apis:health"
                ),
                InlineKeyboardButton(
                    text="🧪 Test APIs", callback_data="admin:apis:test"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📤 Export", callback_data="admin:apis:export"
                ),
                InlineKeyboardButton(
                    text="📥 Import", callback_data="admin:apis:import"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:menu")],
        ]
    )


def api_actions_keyboard(api_id: str) -> InlineKeyboardMarkup:
    """Create keyboard for individual API actions"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✏️ Edit", callback_data=f"admin:api:edit:{api_id}"
                ),
                InlineKeyboardButton(
                    text="🧪 Test", callback_data=f"admin:api:test:{api_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📊 Metrics", callback_data=f"admin:api:metrics:{api_id}"
                ),
                InlineKeyboardButton(
                    text="🏥 Health", callback_data=f"admin:api:health:{api_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Toggle Status", callback_data=f"admin:api:toggle:{api_id}"
                ),
                InlineKeyboardButton(
                    text="🗑️ Delete", callback_data=f"admin:api:delete:{api_id}"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:apis:list")],
        ]
    )


def api_status_filter_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for filtering APIs by status"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🟢 Active", callback_data="admin:apis:filter:active"
                ),
                InlineKeyboardButton(
                    text="🔴 Inactive", callback_data="admin:apis:filter:inactive"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="⚠️ Error", callback_data="admin:apis:filter:error"
                ),
                InlineKeyboardButton(
                    text="🔧 Maintenance", callback_data="admin:apis:filter:maintenance"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📋 All", callback_data="admin:apis:filter:all"
                ),
                InlineKeyboardButton(text="⬅️ Back", callback_data="admin:apis"),
            ],
        ]
    )


def api_environment_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for selecting API environment"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🧪 Development", callback_data="env:development"
                ),
                InlineKeyboardButton(text="🎭 Staging", callback_data="env:staging"),
            ],
            [
                InlineKeyboardButton(
                    text="🚀 Production", callback_data="env:production"
                ),
                InlineKeyboardButton(text="🔬 Testing", callback_data="env:testing"),
            ],
        ]
    )


def api_auth_type_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for selecting authentication type"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(text="🔑 API Key", callback_data="auth:api_key"),
                InlineKeyboardButton(
                    text="🎫 Bearer Token", callback_data="auth:bearer_token"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔐 Basic Auth", callback_data="auth:basic_auth"
                ),
                InlineKeyboardButton(text="🔒 OAuth2", callback_data="auth:oauth2"),
            ],
            [
                InlineKeyboardButton(
                    text="📋 Custom Header", callback_data="auth:custom_header"
                ),
                InlineKeyboardButton(text="❌ None", callback_data="auth:none"),
            ],
        ]
    )


def api_bulk_actions_keyboard(selected_apis: List[str]) -> InlineKeyboardMarkup:
    """Create keyboard for bulk API operations"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🟢 Activate All",
                    callback_data=f"admin:apis:bulk:activate:{','.join(selected_apis)}",
                ),
                InlineKeyboardButton(
                    text="🔴 Deactivate All",
                    callback_data=f"admin:apis:bulk:deactivate:{','.join(selected_apis)}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🧪 Test All",
                    callback_data=f"admin:apis:bulk:test:{','.join(selected_apis)}",
                ),
                InlineKeyboardButton(
                    text="📊 Export Selected",
                    callback_data=f"admin:apis:bulk:export:{','.join(selected_apis)}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🗑️ Delete All",
                    callback_data=f"admin:apis:bulk:delete:{','.join(selected_apis)}",
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:apis:list")],
        ]
    )


def api_health_dashboard_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for API health dashboard"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Status", callback_data="admin:apis:health:refresh"
                ),
                InlineKeyboardButton(
                    text="🧪 Test All", callback_data="admin:apis:health:test_all"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="⚠️ Show Unhealthy", callback_data="admin:apis:health:unhealthy"
                ),
                InlineKeyboardButton(
                    text="📊 Health History", callback_data="admin:apis:health:history"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="⚙️ Configure Checks", callback_data="admin:apis:health:config"
                ),
                InlineKeyboardButton(
                    text="📧 Alert Settings", callback_data="admin:apis:health:alerts"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:apis")],
        ]
    )


def api_metrics_period_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for selecting metrics time period"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📅 Last 24h", callback_data="admin:apis:metrics:24h"
                ),
                InlineKeyboardButton(
                    text="📅 Last 7d", callback_data="admin:apis:metrics:7d"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📅 Last 30d", callback_data="admin:apis:metrics:30d"
                ),
                InlineKeyboardButton(
                    text="📅 Last 90d", callback_data="admin:apis:metrics:90d"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📊 Custom Range", callback_data="admin:apis:metrics:custom"
                ),
                InlineKeyboardButton(
                    text="📈 Real-time", callback_data="admin:apis:metrics:realtime"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:apis:analytics")],
        ]
    )


def api_import_format_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for selecting import format"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📄 JSON File", callback_data="admin:apis:import:json"
                ),
                InlineKeyboardButton(
                    text="📋 YAML File", callback_data="admin:apis:import:yaml"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔗 From URL", callback_data="admin:apis:import:url"
                ),
                InlineKeyboardButton(
                    text="📝 Paste Data", callback_data="admin:apis:import:paste"
                ),
            ],
            [InlineKeyboardButton(text="⬅️ Back", callback_data="admin:apis")],
        ]
    )


def api_test_options_keyboard(api_id: str) -> InlineKeyboardMarkup:
    """Create keyboard for API testing options"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🏥 Health Check",
                    callback_data=f"admin:api:test:health:{api_id}",
                ),
                InlineKeyboardButton(
                    text="🔗 Custom Endpoint",
                    callback_data=f"admin:api:test:custom:{api_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📊 Load Test", callback_data=f"admin:api:test:load:{api_id}"
                ),
                InlineKeyboardButton(
                    text="🔐 Auth Test", callback_data=f"admin:api:test:auth:{api_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📝 Request Builder",
                    callback_data=f"admin:api:test:builder:{api_id}",
                ),
                InlineKeyboardButton(
                    text="📋 Test History",
                    callback_data=f"admin:api:test:history:{api_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="⬅️ Back", callback_data=f"admin:api:view:{api_id}"
                )
            ],
        ]
    )


def api_edit_field_keyboard(api_id: str) -> InlineKeyboardMarkup:
    """Create keyboard for selecting which API field to edit"""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📝 Name", callback_data=f"admin:api:edit:name:{api_id}"
                ),
                InlineKeyboardButton(
                    text="📄 Description",
                    callback_data=f"admin:api:edit:description:{api_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔗 Base URL", callback_data=f"admin:api:edit:url:{api_id}"
                ),
                InlineKeyboardButton(
                    text="🌍 Environment",
                    callback_data=f"admin:api:edit:environment:{api_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔐 Authentication",
                    callback_data=f"admin:api:edit:auth:{api_id}",
                ),
                InlineKeyboardButton(
                    text="⏱️ Timeouts", callback_data=f"admin:api:edit:timeouts:{api_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🚦 Rate Limits",
                    callback_data=f"admin:api:edit:ratelimit:{api_id}",
                ),
                InlineKeyboardButton(
                    text="🏥 Health Checks",
                    callback_data=f"admin:api:edit:health:{api_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🏷️ Tags", callback_data=f"admin:api:edit:tags:{api_id}"
                ),
                InlineKeyboardButton(
                    text="📚 Documentation",
                    callback_data=f"admin:api:edit:docs:{api_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="⬅️ Back", callback_data=f"admin:api:view:{api_id}"
                )
            ],
        ]
    )
